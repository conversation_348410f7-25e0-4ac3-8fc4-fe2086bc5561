<?php

namespace xiled\ces\config;

/**
 * Configuration class for enchantment settings
 */
class EnchantmentConfig {

    /**
     * Activation chance settings for core 4 enchantments only
     * Format: [enchantment name => [base chance, multiplier per level]]
     *
     * Base chance: The base chance (out of 100) for the enchantment to activate
     * Multiplier: How much each level increases the chance
     * Balanced for fair gameplay
     */
    public static array $activationChances = [
        // Default values
        "default" => [5, 3], // 5% base chance, +3% per level

        // Core 4 enchantments only
        "Zeus" => [3, 2],     // 3% base chance, +2% per level
        "Kaboom" => [4, 2],   // 4% base chance, +2% per level
        "Lifesteal" => [5, 3], // 5% base chance, +3% per level (fixed naming)
        "Overload" => [0, 0], // Passive effect, no activation chance needed
    ];

    /**
     * Damage multipliers for core 4 enchantments only
     * Format: [enchantment name => multiplier per level]
     */
    public static array $damageMultipliers = [
        // Default values
        "default" => 1.0, // 1.0x damage per level

        // Core 4 enchantments only
        "Zeus" => 3.0,    // 3.0x damage per level
        "Kaboom" => 1.5,  // 1.5x damage per level
        "Lifesteal" => 1.0, // 1.0x damage per level (health transfer, fixed naming)
        "Overload" => 0.0, // No damage multiplier (adds health)
    ];

    /**
     * Sound effects for core 4 enchantments only
     * Format: [enchantment name => sound name]
     */
    public static array $soundEffects = [
        "Zeus" => "ambient.weather.thunder",
        "Kaboom" => "random.explode",
        "Lifesteal" => "random.drink", // Fixed naming consistency
        "Overload" => "random.levelup",
    ];

    /**
     * Cooldown times for core 4 enchantments only (in ticks, 20 ticks = 1 second)
     * Format: [enchantment name => cooldown ticks]
     * Balanced for fair gameplay
     */
    public static array $cooldownTimes = [
        "default" => 120,   // 6 seconds
        "Zeus" => 240,      // 12 seconds
        "Kaboom" => 220,    // 11 seconds
        "Lifesteal" => 200, // 10 seconds (fixed naming)
        "Overload" => 1200, // 60 seconds (passive effect)
    ];

    /**
     * Maximum hits before guaranteed activation for core 4 enchantments only
     * Format: [enchantment name => max hits]
     * Balanced for fair gameplay
     */
    public static array $maxHitsBeforeActivation = [
        "default" => 15,  // Default: 15 hits max
        "Zeus" => 15,     // Activate after 15 hits max
        "Kaboom" => 14,   // Activate after 14 hits max
        "Lifesteal" => 12, // Activate after 12 hits max (fixed naming)
        "Overload" => 1,  // Always active (passive)
    ];

    /**
     * Calculate the activation chance for an enchantment
     *
     * @param string $enchantName The name of the enchantment
     * @param int $level The level of the enchantment
     * @return int The chance (out of 100) for the enchantment to activate
     */
    public static function getActivationChance(string $enchantName, int $level): int {
        $settings = self::$activationChances[$enchantName] ?? self::$activationChances["default"];
        $baseChance = $settings[0];
        $multiplier = $settings[1];

        return min(100, $baseChance + ($level - 1) * $multiplier);
    }

    /**
     * Get the damage multiplier for an enchantment
     *
     * @param string $enchantName The name of the enchantment
     * @param int $level The level of the enchantment
     * @return float The damage multiplier
     */
    public static function getDamageMultiplier(string $enchantName, int $level): float {
        $multiplier = self::$damageMultipliers[$enchantName] ?? self::$damageMultipliers["default"];

        return $multiplier * $level;
    }

    /**
     * Get the sound effect for an enchantment
     *
     * @param string $enchantName The name of the enchantment
     * @return string|null The sound name or null if no sound is defined
     */
    public static function getSoundEffect(string $enchantName): ?string {
        return self::$soundEffects[$enchantName] ?? null;
    }
}
