# نظام التطويرات - وثائق منع Scope Creep

## 🎯 الهدف الأساسي
هذا النظام مصمم للتركيز على **4 تطويرات أساسية فقط** لمنع التوسع غير المرغوب فيه (Scope Creep).

## ✅ التطويرات المسموحة (4 فقط)

### 1. Overload (الدروع)
- **الوظيفة**: يضيف +4 قلوب إضافية
- **السعر**: 100 ذهب
- **الشروط**: درع دايموند
- **النوع**: تأثير سلبي (دائم)

### 2. Zeus (السيوف)
- **الوظيفة**: يضرب البرق على الأعداء
- **السعر**: 150 ذهب
- **الشروط**: سيف دايموند
- **فرصة التفعيل**: 3% + 2% لكل مستوى

### 3. <PERSON><PERSON><PERSON> (السيوف)
- **الوظيفة**: ينشئ انفجارات على الأعداء
- **السعر**: 120 ذهب
- **الشروط**: سيف دايموند
- **فرصة التفعيل**: 4% + 2% لكل مستوى

### 4. Lifesteal (السيوف)
- **الوظيفة**: يسرق الصحة من الأعداء
- **السعر**: 130 ذهب
- **الشروط**: سيف دايموند
- **فرصة التفعيل**: 5% + 3% لكل مستوى

## ❌ التطويرات المحظورة
جميع التطويرات الأخرى محظورة لمنع Scope Creep، بما في ذلك:
- Bleed, Daze, Frost, Hades, OOF, Poison, Uplift
- Dodge, Drain, Lightning, Monopolize, Paralyze, Shatter, Stun, Velocity, Wither
- Bless, Evade, Immunity, Resist
- Piercing وأي تطويرات أخرى

## 🏗️ هيكل النظام

### الملفات الأساسية
```
src/xiled/ces/
├── Main.php                    # الملف الرئيسي (يسجل 4 تطويرات فقط)
├── QShop.php                   # متجر التطويرات (يعرض 4 تطويرات فقط)
├── config/
│   └── EnchantmentConfig.php   # إعدادات التطويرات (4 فقط)
├── types/
│   ├── KaboomEnchant.php       # تطوير Kaboom
│   ├── LifestealEnchant.php    # تطوير Lifesteal
│   ├── ZeusEnchant.php         # تطوير Zeus
│   ├── OverloadEnchantment.php # تطوير Overload
│   └── ShockEnchantment.php    # الكلاس الأساسي
└── resources/
    └── config.yml              # إعدادات المتجر (4 تطويرات فقط)
```

### قواعد التطوير

#### ✅ مسموح:
- تحسين التطويرات الأربعة الموجودة
- إصلاح الأخطاء في التطويرات الأربعة
- تحسين الأداء والاستقرار
- تحديث الرسائل والأصوات
- تعديل فرص التفعيل للتوازن

#### ❌ ممنوع:
- إضافة تطويرات جديدة
- تفعيل تطويرات محظورة
- تغيير عدد التطويرات من 4
- إضافة أنواع أسلحة جديدة
- تعقيد النظام بميزات غير ضرورية

## 🔧 كيفية الحفاظ على التركيز

### 1. مراجعة الكود
قبل أي تعديل، تأكد من:
- عدم إضافة `use` statements لتطويرات جديدة
- عدم تسجيل تطويرات جديدة في `Main.php`
- عدم إضافة تطويرات في `QShop.php`
- عدم إضافة إعدادات جديدة في `config.yml`

### 2. اختبار النظام
استخدم `src/test_core_enchantments.md` للتأكد من:
- عمل التطويرات الأربعة فقط
- عدم ظهور تطويرات أخرى
- صحة الأسعار والشروط

### 3. مراجعة الطلبات
عند طلب ميزات جديدة، اسأل:
- هل هذا يحسن التطويرات الأربعة الموجودة؟
- هل هذا ضروري للوظيفة الأساسية؟
- هل سيؤدي هذا إلى تعقيد النظام؟

## 📋 قائمة مراجعة منع Scope Creep

### قبل أي تعديل:
- [ ] هل التعديل يخص التطويرات الأربعة فقط؟
- [ ] هل التعديل يحسن الوظيفة الموجودة؟
- [ ] هل التعديل لا يضيف تعقيد غير ضروري؟
- [ ] هل تم اختبار التعديل مع التطويرات الأربعة؟

### بعد أي تعديل:
- [ ] هل المتجر يعرض 4 تطويرات فقط؟
- [ ] هل جميع التطويرات تعمل بشكل صحيح؟
- [ ] هل لا توجد أخطاء في console؟
- [ ] هل الأسعار والشروط صحيحة؟

## 🚨 علامات تحذير Scope Creep

إذا رأيت أي من هذه العلامات، توقف فوراً:
- طلب إضافة تطوير جديد
- طلب دعم أسلحة جديدة
- طلب ميزات معقدة غير ضرورية
- زيادة عدد التطويرات عن 4
- إضافة أنظمة فرعية جديدة

## 📞 الدعم والمساعدة
عند الحاجة للمساعدة:
1. راجع هذه الوثائق أولاً
2. اختبر النظام باستخدام `test_core_enchantments.md`
3. تأكد من التركيز على التطويرات الأربعة فقط
4. لا تضيف ميزات جديدة بدون مراجعة هذه الوثائق

## 🎉 الخلاصة
هذا النظام مصمم ليكون **بسيط ومركز ومستقر**. التركيز على 4 تطويرات فقط يضمن:
- سهولة الصيانة
- استقرار النظام
- تجربة لعب متوازنة
- عدم التعقيد غير الضروري

**تذكر: البساطة هي الكمال النهائي!**
