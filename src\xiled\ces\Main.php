<?php

namespace xiled\ces;

use xiled\ces\commands\EnchantCommand;
use xiled\ces\commands\EnchanterCommand;
use xiled\ces\types\{
	KaboomEnchant,
	LifestealEnchant,
	ZeusEnchant
};
use xiled\ces\manager\EnchantmentManager;
use pocketmine\scheduler\ClosureTask;
use xiled\ces\types\OverloadEnchantment;
use xiled\ces\types\ShockEnchantment;
use xiled\ces\types\StunEnchantment;
use xiled\ces\types\VelocityEnchantment;
use xiled\ces\types\WitherEnchantment;
use pocketmine\data\bedrock\EnchantmentIdMap;
use pocketmine\item\enchantment\EnchantmentInstance;
use pocketmine\item\enchantment\Rarity;
use pocketmine\item\enchantment\VanillaEnchantments;
use pocketmine\item\Item;
use pocketmine\item\VanillaItems;
use pocketmine\player\Player;
use pocketmine\Server;
use pocketmine\utils\TextFormat;
use pocketmine\plugin\PluginBase;
use pocketmine\utils\SingletonTrait;
use muqsit\invmenu\InvMenuHandler;
use xiled\ces\QShop;
use xiled\ces\commands\QShopCommand;
use xiled\ces\commands\TestCommand;
use xiled\ces\commands\TestEnchantCommand;
use xiled\ces\commands\TestMessageCommand;
use xiled\ces\utils\MessageManager;
use pocketmine\utils\Config;

class Main extends PluginBase
{
	use SingletonTrait;

    private static $enchantId = 1001;

    /** @var array */
    private static array $registeredEnchants = [];

    /** @var array */
    private static array $classifiedEnchantments = [];

    /** @var ShockEnchantment[] */
    private static array $enchants = [];

    /** @var MessageManager */
    private MessageManager $messageManager;

    /** @var array */
    private array $enchantmentConfig = [];

    public function onEnable():void{
    	if(!InvMenuHandler::isRegistered()){
    	    InvMenuHandler::register($this);
    	}

    	// Initialize QShop functionality
    	$this->saveDefaultConfig();
    	$this->messageManager = new MessageManager($this);
    	$this->loadEnchantmentConfig();

    	// Register commands
    	try {
    	    // Register test command first
    	    $testCommand = new TestCommand($this);
    	    $this->getServer()->getCommandMap()->register("qtest", $testCommand);
    	    $this->getLogger()->info("Test command registered successfully!");

    	    // Register test enchant command
    	    $testEnchantCommand = new TestEnchantCommand($this);
    	    $this->getServer()->getCommandMap()->register("testenchant", $testEnchantCommand);
    	    $this->getLogger()->info("Test enchant command registered successfully!");

    	    // Register test message command
    	    $testMessageCommand = new TestMessageCommand($this);
    	    $this->getServer()->getCommandMap()->register("testmsg", $testMessageCommand);
    	    $this->getLogger()->info("Test message command registered successfully!");

    	    // Register QShop command
    	    $qshopCommand = new QShopCommand($this);
    	    $this->getServer()->getCommandMap()->register("qshop", $qshopCommand);
    	    $this->getLogger()->info("QShop command registered successfully!");
    	} catch (\Throwable $e) {
    	    $this->getLogger()->error("Failed to register commands: " . $e->getMessage());
    	}

    	self::init();

    	// Schedule the luck update task to run every second (20 ticks)
    	$this->getScheduler()->scheduleRepeatingTask(new ClosureTask(
    	    function(): void {
    	        EnchantmentManager::updateLuck();
    	    }
    	), 20);
    }

    public function onLoad():void{
        self::setInstance($this);
    }

    /**
     * Load enchantment configuration for QShop
     */
    private function loadEnchantmentConfig(): void {
        $this->enchantmentConfig = [
            "Overload" => [
                "price" => $this->getConfigValue("enchantments.overload.price", 100),
                "max_level" => $this->getConfigValue("enchantments.overload.max_level", 1),
                "display" => [
                    "name" => $this->getConfigValue("enchantments.overload.display.name", "§r§l§bOverload"),
                    "lore" => $this->getConfigValue("enchantments.overload.display.lore", [
                        "§r§7Price: §6%price% Gold",
                        "§r§7Max Level: §6%max_level%",
                        "",
                        "§r§eClick to purchase!"
                    ])
                ]
            ],
            "Zeus" => [
                "price" => $this->getConfigValue("enchantments.zeus.price", 150),
                "max_level" => $this->getConfigValue("enchantments.zeus.max_level", 1),
                "display" => [
                    "name" => $this->getConfigValue("enchantments.zeus.display.name", "§r§l§fZeus"),
                    "lore" => $this->getConfigValue("enchantments.zeus.display.lore", [
                        "§r§7Price: §6%price% Gold",
                        "§r§7Max Level: §6%max_level%",
                        "",
                        "§r§eClick to purchase!"
                    ])
                ]
            ],
            "Kaboom" => [
                "price" => $this->getConfigValue("enchantments.kaboom.price", 120),
                "max_level" => $this->getConfigValue("enchantments.kaboom.max_level", 1),
                "display" => [
                    "name" => $this->getConfigValue("enchantments.kaboom.display.name", "§r§l§dKaboom"),
                    "lore" => $this->getConfigValue("enchantments.kaboom.display.lore", [
                        "§r§7Price: §6%price% Gold",
                        "§r§7Max Level: §6%max_level%",
                        "",
                        "§r§eClick to purchase!"
                    ])
                ]
            ],
            "Lifesteal" => [
                "price" => $this->getConfigValue("enchantments.lifesteal.price", 130),
                "max_level" => $this->getConfigValue("enchantments.lifesteal.max_level", 1),
                "display" => [
                    "name" => $this->getConfigValue("enchantments.lifesteal.display.name", "§r§l§cLifesteal"),
                    "lore" => $this->getConfigValue("enchantments.lifesteal.display.lore", [
                        "§r§7Price: §6%price% Gold",
                        "§r§7Max Level: §6%max_level%",
                        "",
                        "§r§eClick to purchase!"
                    ])
                ]
            ]
        ];
    }

    /**
     * Get a configuration value with a default fallback
     *
     * @param string $key The configuration key
     * @param mixed $default The default value
     * @return mixed The configuration value
     */
    public function getConfigValue(string $key, $default = null) {
        return $this->getConfig()->getNested($key, $default);
    }

    /**
     * Get the price of an enchantment
     *
     * @param string $enchantName The name of the enchantment
     * @return int The price of the enchantment
     */
    public function getEnchantmentPrice(string $enchantName): int {
        return $this->enchantmentConfig[$enchantName]["price"] ?? 100;
    }

    /**
     * Get the maximum level of an enchantment
     *
     * @param string $enchantName The name of the enchantment
     * @return int The maximum level of the enchantment
     */
    public function getEnchantmentMaxLevel(string $enchantName): int {
        return $this->enchantmentConfig[$enchantName]["max_level"] ?? 5;
    }

    /**
     * Get the display settings for an enchantment
     *
     * @param string $enchantName The name of the enchantment
     * @return array The display settings
     */
    public function getEnchantmentDisplay(string $enchantName): array {
        return $this->enchantmentConfig[$enchantName]["display"] ?? [];
    }

    /**
     * Get the message manager
     *
     * @return MessageManager The message manager
     */
    public function getMessageManager(): MessageManager {
        return $this->messageManager;
    }
    /**
     * Initialize the enchantment system
     * This method registers all custom enchantments and sets up the necessary event listeners
     */
    public static function init(): void {
        try {
            // Register sword enchantments
            self::registerSwordEnchantments();

            // Register armor enchantments
            self::registerArmorEnchantments();

            // Register bow enchantments
            self::registerBowEnchantments();

            // Register commands
            Server::getInstance()->getCommandMap()->register("enchant", new EnchantCommand());

            // Register event listeners
            Server::getInstance()->getPluginManager()->registerEvents(new EventListener(self::getInstance()), self::getInstance());

            // Log success
            Server::getInstance()->getLogger()->info("[EnchantManager] " . count(self::$registeredEnchants) . " Enchantments loaded successfully");
        } catch (\Throwable $e) {
            // Log error
            Server::getInstance()->getLogger()->error("[EnchantManager] Error initializing enchantments: " . $e->getMessage());
        }
    }

    /**
     * Register core sword enchantments only (to prevent scope creep)
     */
    private static function registerSwordEnchantments(): void {
        // Core 3 sword enchantments only
        self::register(new KaboomEnchant());
        self::register(new LifestealEnchant());
        self::register(new ZeusEnchant());
    }

    /**
     * Register core armor enchantments only (to prevent scope creep)
     */
    private static function registerArmorEnchantments(): void {
        // Core 1 armor enchantment only
        self::register(new OverloadEnchantment());
    }

    /**
     * Register bow enchantments (disabled to prevent scope creep)
     */
    private static function registerBowEnchantments(): void {
        // No bow enchantments registered to keep focus on core 4 enchantments
    }

    /**
     * Register a custom enchantment
     *
     * @param ShockEnchantment $enchant The enchantment to register
     */
    public static function register(ShockEnchantment $enchant): void {
        try {
            // Add to registered enchants array
            self::$registeredEnchants[] = $enchant;

            // Register with the enchantment ID map
            EnchantmentIdMap::getInstance()->register(self::nextRuntimeId(), $enchant);

            // Add to enchants lookup array
            self::$enchants[strtolower($enchant->getName())] = $enchant;

            // Classify by rarity for random selection
            $rarity = $enchant->getRarity();
            if (!isset(self::$classifiedEnchantments[$rarity])) {
                self::$classifiedEnchantments[$rarity] = [];
            }
            self::$classifiedEnchantments[$rarity][] = $enchant;

            // Log success
            self::getInstance()->getLogger()->debug(TextFormat::YELLOW . "Custom Enchants: The '" . $enchant->getName() . "' enchantment was successfully registered");
        } catch (\Throwable $e) {
            // Log error
            self::getInstance()->getLogger()->error(TextFormat::RED . "Failed to register enchantment '" . $enchant->getName() . "': " . $e->getMessage());
        }
    }

    /**
     * Get the next available runtime ID for enchantments
     *
     * @return int The next runtime ID
     */
    public static function nextRuntimeId(): int {
        return self::$enchantId++;
    }

    /**
     * Get all custom enchantments on a player's armor
     *
     * @param Player $player The player to check
     * @return array Array of EnchantmentInstance objects
     */
    public static function getArmorCE(Player $player): array {
        $enchants = [];

        try {
            $armorInv = $player->getArmorInventory();
            if ($armorInv === null) {
                return [];
            }

            // Get all armor pieces
            $armorPieces = [
                $armorInv->getHelmet(),
                $armorInv->getChestplate(),
                $armorInv->getLeggings(),
                $armorInv->getBoots()
            ];

            // Check each armor piece for custom enchantments
            foreach ($armorPieces as $armorPiece) {
                if ($armorPiece->isNull()) {
                    continue;
                }

                foreach ($armorPiece->getEnchantments() as $enchantment) {
                    if ($enchantment->getType() instanceof ShockEnchantment) {
                        $enchants[] = $enchantment;
                    }
                }
            }
        } catch (\Throwable $e) {
            // Log error and return empty array
            self::getInstance()->getLogger()->error("Error getting armor enchantments: " . $e->getMessage());
        }

        return $enchants;
    }

    /**
     * Apply an enchantment to an item and update its lore and name
     *
     * @param Item $item The item to enchant
     * @param EnchantmentInstance $enchantment The enchantment to apply
     * @return Item The enchanted item
     */
    public static function enchantItem(Item $item, EnchantmentInstance $enchantment): Item {
        try {
            // Add the enchantment to the item
            $item->addEnchantment($enchantment);

            // Initialize variables for lore and enchantment count
            $lore = [];
            $enchantCount = 0;
            $enchantNames = [];

            // Add enchantment header to lore
            $lore[] = TextFormat::RED . TextFormat::BOLD . "Enchants:";

            // Add each custom enchantment to the lore
            foreach ($item->getEnchantments() as $enchantmentInstance) {
                $enchantmentType = $enchantmentInstance->getType();
                if ($enchantmentType instanceof ShockEnchantment) {
                    $enchantName = $enchantmentType->getName();
                    $color = TextFormat::RESET . self::enchantNameToColor($enchantName);
                    $level = $enchantmentInstance->getLevel();
                    $romanLevel = self::getRomanNumber($level);

                    // Add formatted enchantment to lore
                    $lore[] = "  " . $color . $enchantName . " " . $romanLevel;

                    // Track enchantment count and names
                    $enchantCount++;
                    $enchantNames[] = $enchantName;
                }
            }

            // Set the updated lore
            $item->setLore($lore);

            // Update the item name to show enchantment count
            if ($enchantCount > 0) {
                $itemName = $item->getName();

                // Remove existing enchantment count from name if present
                if (preg_match('/\s+§7\(§3\d+§7\)$/', $itemName)) {
                    $itemName = preg_replace('/\s+§7\(§3\d+§7\)$/', '', $itemName);
                } else if (preg_match('/\s+\(\d+\)$/', $itemName)) {
                    $itemName = preg_replace('/\s+\(\d+\)$/', '', $itemName);
                }

                // Add new enchantment count to name
                $itemName .= ' §7(§3' . $enchantCount . '§7)';
                $item->setCustomName($itemName);
            }
        } catch (\Throwable $e) {
            // Log error
            self::getInstance()->getLogger()->error("Error enchanting item: " . $e->getMessage());
        }

        return $item;
    }

    /**
     * Get all enchantments of a specific event type
     *
     * @param int $type The event type (e.g., ShockEnchantment::DAMAGE)
     * @return ShockEnchantment[] Array of enchantments of the specified type
     */
    public static function getEnchantsFromType(int $type): array {
        $enchantsArray = [];

        foreach (self::$registeredEnchants as $enchant) {
            if ($enchant->getEventType() === $type) {
                $enchantsArray[] = $enchant;
            }
        }

        return $enchantsArray;
    }

    /**
     * Get a random enchantment of a specific rarity
     *
     * @param int $rarity The rarity level (e.g., Rarity::COMMON)
     * @return ShockEnchantment A random enchantment of the specified rarity
     * @throws \InvalidArgumentException If no enchantments of the specified rarity exist
     */
    public static function getRandomEnchant(int $rarity): ShockEnchantment {
        // Use the classified enchantments array for better performance
        if (isset(self::$classifiedEnchantments[$rarity]) && !empty(self::$classifiedEnchantments[$rarity])) {
            return self::$classifiedEnchantments[$rarity][array_rand(self::$classifiedEnchantments[$rarity])];
        }

        // Fallback to manual search if classified array is not available
        $enchantsArray = [];
        foreach (self::$registeredEnchants as $enchant) {
            if ($enchant->getRarity() === $rarity) {
                $enchantsArray[] = $enchant;
            }
        }

        if (empty($enchantsArray)) {
            throw new \InvalidArgumentException("No enchantments found with rarity: $rarity");
        }

        return $enchantsArray[array_rand($enchantsArray)];
    }

    /**
     * Get a random enchantment, optionally of a specific rarity
     *
     * @param int|null $rarity The rarity level (optional)
     * @return ShockEnchantment A random enchantment
     * @throws \InvalidArgumentException If no enchantments exist or no enchantments of the specified rarity exist
     */
    public static function getRandomEnchantment(?int $rarity = null): ShockEnchantment {
        if (empty(self::$registeredEnchants)) {
            throw new \InvalidArgumentException("No enchantments have been registered");
        }

        if ($rarity !== null) {
            return self::getRandomEnchant($rarity);
        }

        return self::$registeredEnchants[array_rand(self::$registeredEnchants)];
    }

    /**
     * Get an enchantment by its name (case-insensitive)
     *
     * @param string $name The name of the enchantment
     * @return ShockEnchantment|null The enchantment, or null if not found
     */
    public static function getEnchantByName(string $name): ?ShockEnchantment {
        // Use the optimized lookup array for better performance
        return self::getEnchantment($name);
    }

    /**
     * Get an enchantment by its ID
     *
     * @param int $ID The ID of the enchantment
     * @return ShockEnchantment|null The enchantment, or null if not found
     */
    public static function getEnchantByID(int $ID): ?ShockEnchantment {
        foreach (self::$registeredEnchants as $enchant) {
            if ($enchant->getId() === $ID) {
                return $enchant;
            }
        }

        return null;
    }

    /**
     * Get an enchantment by its name (case-insensitive)
     * This is an optimized version using the lookup array
     *
     * @param string $name The name of the enchantment
     * @return ShockEnchantment|null The enchantment, or null if not found
     */
    public static function getEnchantment(string $name): ?ShockEnchantment {
        return self::$enchants[strtolower($name)] ?? null;
    }

    /**
     * Get all registered enchantments as a name-indexed array
     *
     * @return ShockEnchantment[] Array of enchantments indexed by name
     */
    public static function getEnchants(): array
    {
        return self::$enchants;
    }

    /**
     * Get all registered enchantments as a sequential array
     *
     * @return ShockEnchantment[] Array of enchantments
     */
    public static function getRegisteredEnchants(): array {
        return self::$registeredEnchants;
    }

    /**
     * Convert an integer to a Roman numeral
     *
     * @param int $integer The integer to convert
     * @return string The Roman numeral representation
     */
    public static function getRomanNumber(int $integer): string {
        // Handle special cases
        if ($integer <= 0) {
            return "0";
        }

        // Define Roman numeral mappings
        $characters = [
            'M' => 1000,
            'CM' => 900,
            'D' => 500,
            'CD' => 400,
            'C' => 100,
            'XC' => 90,
            'L' => 50,
            'XL' => 40,
            'X' => 10,
            'IX' => 9,
            'V' => 5,
            'IV' => 4,
            'I' => 1
        ];

        $romanString = "";

        // Convert integer to Roman numeral
        while ($integer > 0) {
            foreach ($characters as $rom => $arb) {
                if ($integer >= $arb) {
                    $integer -= $arb;
                    $romanString .= $rom;
                    break;
                }
            }
        }

        return $romanString;
    }

    /**
     * Convert an enchantment rarity to a color
     *
     * @param int $rarity The rarity level (e.g., Rarity::COMMON)
     * @return string The color code for the rarity
     */
    public static function rarityToColor(int $rarity): string {
        return match ($rarity) {
            Rarity::COMMON => TextFormat::AQUA,
            Rarity::UNCOMMON => TextFormat::GREEN,
            Rarity::RARE => TextFormat::YELLOW,
            Rarity::MYTHIC => TextFormat::RED,
            default => TextFormat::WHITE,
        };
    }

    /**
     * Get the color for an enchantment name
     *
     * @param string $enchantName The name of the enchantment
     * @return string The color code for the enchantment
     */
    public static function enchantNameToColor(string $enchantName): string {
        // Convert to lowercase for case-insensitive comparison
        $enchantName = strtolower($enchantName);

        // Define color mappings for core 4 enchantments only
        $colorMap = [
            "kaboom" => TextFormat::LIGHT_PURPLE,
            "overload" => TextFormat::AQUA,
            "zeus" => TextFormat::BLUE,
            "lifesteal" => TextFormat::RED,
        ];

        // Return the color for the enchantment, or default to yellow
        return $colorMap[$enchantName] ?? TextFormat::YELLOW;
    }

    /**
     * Get the armor type and protection level of a player
     *
     * @param Player $victim The player to check
     * @return array [armorType, protectionLevel] where:
     *   - armorType: 0 = no armor, 1 = diamond armor, 2 = other armor
     *   - protectionLevel: average protection level across all armor pieces (0-4)
     */
    public static function getArmorTypeAndProtection(Player $victim): array {
        $armorInventory = $victim->getArmorInventory();

        // Count armor pieces and check if they're diamond
        $armorCount = 0;
        $diamondCount = 0;
        $totalProtection = 0;

        // Check helmet
        $helmet = $armorInventory->getHelmet();
        if (!$helmet->isNull()) {
            $armorCount++;
            if ($helmet->getTypeId() === VanillaItems::DIAMOND_HELMET()->getTypeId()) {
                $diamondCount++;
            }
            $totalProtection += self::getProtectionLevel($helmet);
        }

        // Check chestplate
        $chestplate = $armorInventory->getChestplate();
        if (!$chestplate->isNull()) {
            $armorCount++;
            if ($chestplate->getTypeId() === VanillaItems::DIAMOND_CHESTPLATE()->getTypeId()) {
                $diamondCount++;
            }
            $totalProtection += self::getProtectionLevel($chestplate);
        }

        // Check leggings
        $leggings = $armorInventory->getLeggings();
        if (!$leggings->isNull()) {
            $armorCount++;
            if ($leggings->getTypeId() === VanillaItems::DIAMOND_LEGGINGS()->getTypeId()) {
                $diamondCount++;
            }
            $totalProtection += self::getProtectionLevel($leggings);
        }

        // Check boots
        $boots = $armorInventory->getBoots();
        if (!$boots->isNull()) {
            $armorCount++;
            if ($boots->getTypeId() === VanillaItems::DIAMOND_BOOTS()->getTypeId()) {
                $diamondCount++;
            }
            $totalProtection += self::getProtectionLevel($boots);
        }

        // Determine armor type
        $armorType = 0; // No armor
        if ($armorCount > 0) {
            if ($diamondCount == $armorCount) {
                $armorType = 1; // Full diamond armor
            } else {
                $armorType = 2; // Other armor
            }
        }

        // Calculate average protection level (0-4)
        $avgProtectionLevel = $armorCount > 0 ? $totalProtection / $armorCount : 0;

        return [$armorType, $avgProtectionLevel];
    }

    /**
     * Calculate Zeus damage based on victim's armor type and protection level
     *
     * @param Player $victim The player being affected by Zeus
     * @param int $enchantmentLevel The level of the Zeus enchantment
     * @return float The damage to apply
     */
    public static function calculateZeusDamage(Player $victim, int $enchantmentLevel): float {
        list($armorType, $protectionLevel) = self::getArmorTypeAndProtection($victim);

        // Base damage multiplier from enchantment level
        $baseDamage = $enchantmentLevel * 2;

        // Apply specific damage based on armor type and protection
        if ($armorType == 0) {
            // No armor - instant death (20 hearts)
            return 20.0;
        } elseif ($armorType == 1) {
            // Diamond armor
            if ($protectionLevel >= 4) {
                // Protection IV - 4 hearts
                return 4.0;
            } elseif ($protectionLevel >= 3) {
                // Protection III - 5 hearts
                return 5.0;
            } else {
                // No protection or lower - 6 hearts
                return 6.0;
            }
        } else {
            // Other armor types - scale between 6-8 hearts based on protection
            return 8.0 - ($protectionLevel * 0.5);
        }
    }

    /**
     * Calculate Kaboom damage based on victim's armor type and protection level
     *
     * @param Player $victim The player being affected by Kaboom
     * @param int $enchantmentLevel The level of the Kaboom enchantment
     * @return float The damage to apply
     */
    public static function calculateKaboomDamage(Player $victim, int $enchantmentLevel): float {
        list($armorType, $protectionLevel) = self::getArmorTypeAndProtection($victim);

        // Base damage multiplier from enchantment level
        $baseDamage = $enchantmentLevel * 1.5;

        // Apply specific damage based on armor type and protection
        if ($armorType == 0) {
            // No armor - leave with half heart (current health - 0.5)
            return max(0, $victim->getHealth() - 0.5);
        } elseif ($armorType == 1) {
            // Diamond armor
            if ($protectionLevel >= 4) {
                // Protection IV - 3 hearts
                return 3.0;
            } elseif ($protectionLevel >= 3) {
                // Protection III - 3.5 hearts
                return 3.5;
            } else {
                // No protection or lower - 4 hearts
                return 4.0;
            }
        } else {
            // Other armor types - scale between 4-5 hearts based on protection
            return 5.0 - ($protectionLevel * 0.25);
        }
    }

    /**
     * Calculate Lifesteal amount based on victim's armor type and protection level
     *
     * @param Player $victim The player being affected by Lifesteal
     * @param int $enchantmentLevel The level of the Lifesteal enchantment
     * @return float The amount of health to steal
     */
    public static function calculateLifestealAmount(Player $victim, int $enchantmentLevel): float {
        list($armorType, $protectionLevel) = self::getArmorTypeAndProtection($victim);

        // Base lifesteal amount from enchantment level
        $baseAmount = $enchantmentLevel * 0.5;

        // Apply specific amount based on armor type and protection
        if ($armorType == 0) {
            // No armor - maximum lifesteal (2.5 hearts)
            return min(2.5, $victim->getHealth() * 0.25);
        } elseif ($armorType == 1) {
            // Diamond armor
            if ($protectionLevel >= 4) {
                // Protection IV - 1 heart
                return 1.0;
            } elseif ($protectionLevel >= 3) {
                // Protection III - 1.5 hearts
                return 1.5;
            } else {
                // No protection or lower - 2 hearts
                return 2.0;
            }
        } else {
            // Other armor types - scale between 1.5-2 hearts based on protection
            return 2.0 - ($protectionLevel * 0.125);
        }
    }

    /**
     * Check if an item is diamond armor
     *
     * @param Item $item The item to check
     * @return bool True if the item is diamond armor, false otherwise
     */
    public static function isDiamondArmor(Item $item): bool {
        if (!$item->isNull()) {
            return in_array($item->getTypeId(), [
                VanillaItems::DIAMOND_HELMET()->getTypeId(),
                VanillaItems::DIAMOND_CHESTPLATE()->getTypeId(),
                VanillaItems::DIAMOND_LEGGINGS()->getTypeId(),
                VanillaItems::DIAMOND_BOOTS()->getTypeId()
            ]);
        }
        return false;
    }

    /**
     * Get the protection level of an armor item
     *
     * @param Item $item The armor item to check
     * @return int The protection level (0-4)
     */
    private static function getProtectionLevel(Item $item): int {
        // Default protection level if no enchantment
        $protectionLevel = 0;

        // Try to get protection level from item
        try {
            // Check for vanilla Protection enchantment
            foreach ($item->getEnchantments() as $enchantment) {
                $enchantmentType = $enchantment->getType();

                // Check if it's the Protection enchantment by comparing with VanillaEnchantments
                if ($enchantmentType->getId() === VanillaEnchantments::PROTECTION()->getId()) {
                    $protectionLevel = $enchantment->getLevel(); // Level 1-4
                    break;
                }
            }
        } catch (\Throwable $e) {
            // If any error occurs, just return 0
            return 0;
        }

        return $protectionLevel;
    }
}
