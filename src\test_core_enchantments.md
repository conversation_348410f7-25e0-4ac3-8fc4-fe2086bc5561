# اختبار التطويرات الأربعة الأساسية

## الهدف
التأكد من أن التطويرات الأربعة الأساسية فقط تعمل بشكل صحيح:
- **Overload** (للدروع)
- **Zeus** (للسيوف)
- **Kaboom** (للسيوف)
- **Lifesteal** (للسيوف)

## خطوات الاختبار

### 1. اختبار النظام الأساسي
```
/qtest  - للتأكد من أن النظام يعمل
/qshop  - لفتح متجر التطوير
```

### 2. اختبار تطوير Overload (الدرع)
1. **البس درع دايموند كامل**
2. **ضع ذهب في المخزون** (100+ قطعة ذهب)  
3. **اكتب `/qshop`** لفتح المتجر
4. **اضغط على Overload**
5. **تحقق من النتائج:**
   - يجب أن تحصل على +4 قلوب إضافية
   - يجب أن يظهر "Overload activated! +4 hearts"
   - يجب أن يتم خصم 100 ذهب

### 3. اختبار تطوير Zeus (السيف)
1. **احمل سيف دايموند** في يدك
2. **ضع ذهب في المخزون** (150+ قطعة ذهب)
3. **اكتب `/qshop`** لفتح المتجر
4. **اضغط على Zeus**
5. **تحقق من النتائج:**
   - يجب أن يتم تطوير السيف
   - يجب أن يتم خصم 150 ذهب
   - عند الضرب: يجب أن تظهر البرق أحياناً

### 4. اختبار تطوير Kaboom (السيف)
1. **احمل سيف دايموند** في يدك
2. **ضع ذهب في المخزون** (120+ قطعة ذهب)
3. **اكتب `/qshop`** لفتح المتجر
4. **اضغط على Kaboom**
5. **تحقق من النتائج:**
   - يجب أن يتم تطوير السيف
   - يجب أن يتم خصم 120 ذهب
   - عند الضرب: يجب أن تظهر انفجارات أحياناً

### 5. اختبار تطوير Lifesteal (السيف)
1. **احمل سيف دايموند** في يدك
2. **ضع ذهب في المخزون** (130+ قطعة ذهب)
3. **اكتب `/qshop`** لفتح المتجر
4. **اضغط على Lifesteal**
5. **تحقق من النتائج:**
   - يجب أن يتم تطوير السيف
   - يجب أن يتم خصم 130 ذهب
   - عند الضرب: يجب أن تسرق صحة أحياناً

## النتائج المتوقعة

### ✅ يجب أن يعمل:
- فتح المتجر بـ `/qshop`
- شراء التطويرات الأربعة فقط
- تطبيق التطويرات على الأدوات الصحيحة
- خصم الذهب بشكل صحيح
- تفعيل التطويرات أثناء القتال

### ❌ يجب ألا يعمل:
- أي تطويرات أخرى غير الأربعة الأساسية
- شراء تطوير بدون الأداة المطلوبة
- شراء تطوير بدون ذهب كافي
- تطبيق تطوير على أداة خاطئة

## ملاحظات مهمة
- **Overload** يعمل فقط مع الدروع الدايموند
- **Zeus, Kaboom, Lifesteal** تعمل فقط مع السيوف الدايموند
- جميع التطويرات لها فرصة تفعيل محددة (ليست 100%)
- التطويرات الأخرى يجب ألا تظهر في المتجر أو تعمل

## كيفية التحقق من النجاح
1. **لا توجد أخطاء** في console
2. **المتجر يعرض 4 تطويرات فقط**
3. **جميع التطويرات تعمل كما هو متوقع**
4. **لا توجد تطويرات أخرى مفعلة**
